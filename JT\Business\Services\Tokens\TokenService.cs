using JT.Content.Client;
using JT.Business.Models;
using JT.Content.Client.Models;

namespace JT.Business.Services.Tokens;

public class TokenService(JTApiClient api) : ITokenService
{

	public async ValueTask<IImmutableList<TokenTransaction>> GetUserTransactions(Guid userId, CancellationToken ct = default)
	{
		try
		{
			// Note: The API currently returns a single transaction, but we need a collection
			// This should be updated when the API supports returning multiple transactions
			var transactionData = await api.Api.Token.User[userId].Transactions.GetAsync(cancellationToken: ct);
			if (transactionData != null)
			{
				return ImmutableList.Create(new TokenTransaction(transactionData));
			}
			return ImmutableList<TokenTransaction>.Empty;
		}
		catch
		{
			return ImmutableList<TokenTransaction>.Empty;
		}
	}

	public async ValueTask<int> GetUserBalance(Guid userId, CancellationToken ct = default)
	{
		try
		{
			var userData = await api.Api.Token.User[userId].Balance.GetAsync(cancellationToken: ct);
			return userData?.TokenBalance ?? 0;
		}
		catch
		{
			return 0;
		}
	}

	public async ValueTask<TokenTransaction> AddTokens(Guid userId, int amount, string type, string description, string? referenceId = null, CancellationToken ct = default)
	{
		try
		{
			var request = new AddTokensRequest
			{
				Amount = amount,
				Type = type,
				Description = description,
				ReferenceId = referenceId
			};

			var transactionData = await api.Api.Token.User[userId].Add.PostAsync(request, cancellationToken: ct);
			return new TokenTransaction(transactionData ?? throw new InvalidOperationException("Failed to add tokens - no transaction data returned"));
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to add tokens: {ex.Message}", ex);
		}
	}

	public async ValueTask<TokenTransaction> DeductTokens(Guid userId, int amount, string type, string description, string? referenceId = null, CancellationToken ct = default)
	{
		try
		{
			var request = new DeductTokensRequest
			{
				Amount = amount,
				Type = type,
				Description = description,
				ReferenceId = referenceId
			};

			var transactionData = await api.Api.Token.User[userId].Deduct.PostAsync(request, cancellationToken: ct);
			return new TokenTransaction(transactionData ?? throw new InvalidOperationException("Failed to deduct tokens - no transaction data returned"));
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to deduct tokens: {ex.Message}", ex);
		}
	}

	public async ValueTask<bool> HasSufficientBalance(Guid userId, int requiredAmount, CancellationToken ct = default)
	{
		var balance = await GetUserBalance(userId, ct);
		return balance >= requiredAmount;
	}

	public async ValueTask<TokenTransaction> ProcessReferralBonus(Guid referrerId, Guid referredUserId, CancellationToken ct = default)
	{
		// Standard referral bonus amount
		const int referralBonus = 25;
		
		var description = $"Referral bonus for inviting user {referredUserId}";
		return await AddTokens(referrerId, referralBonus, "referral", description, referredUserId.ToString(), ct);
	}

	public async ValueTask<TokenTransaction> ProcessWelcomeBonus(Guid userId, CancellationToken ct = default)
	{
		// Standard welcome bonus amount
		const int welcomeBonus = 25;
		
		var description = "Welcome bonus for joining JobTransfer";
		return await AddTokens(userId, welcomeBonus, "bonus", description, "welcome", ct);
	}

	public async ValueTask<TokenTransaction> ProcessPurchaseBonus(Guid userId, string subscriptionTier, CancellationToken ct = default)
	{
		// Bonus amounts based on subscription tier
		var bonusAmount = subscriptionTier.ToLower() switch
		{
			"silver" => 25,
			"gold" => 50,
			"diamond" => 100,
			_ => 0
		};

		if (bonusAmount > 0)
		{
			var description = $"Purchase bonus for {subscriptionTier} subscription";
			return await AddTokens(userId, bonusAmount, "purchase", description, subscriptionTier, ct);
		}

		throw new InvalidOperationException($"No bonus available for subscription tier: {subscriptionTier}");
	}

	public async ValueTask<IImmutableList<TokenTransaction>> GetTransactionsByType(string type, CancellationToken ct = default)
	{
		// This would typically be implemented with a server-side filter
		// For now, we'll get all transactions and filter client-side (not ideal for production)
		try
		{
			// Note: This is a simplified implementation that would need to be enhanced
			// when the API supports getting all transactions or filtering by type
			// For now, we return empty as we don't have a way to get all transactions across users
			await Task.CompletedTask; // Remove async warning
			return ImmutableList<TokenTransaction>.Empty;
		}
		catch
		{
			return ImmutableList<TokenTransaction>.Empty;
		}
	}
}
