{"descriptionHash": "E54CEEE95E395E3448AF5A50387E0CF8EA42B8AA37E1BE15955DA86F5AF7AE21905F05795623802102DC4172A73A46D931D6327122661585850B7935B814E203", "descriptionLocation": "../../Specs/JTApiClient.swagger.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.27.0", "clientClassName": "JTApiClient", "typeAccessModifier": "Public", "clientNamespaceName": "JT.Content.Client", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": false, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory", "Microsoft.Kiota.Serialization.Text.TextSerializationWriterFactory", "Microsoft.Kiota.Serialization.Form.FormSerializationWriterFactory", "Microsoft.Kiota.Serialization.Multipart.MultipartSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory", "Microsoft.Kiota.Serialization.Text.TextParseNodeFactory", "Microsoft.Kiota.Serialization.Form.FormParseNodeFactory"], "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}